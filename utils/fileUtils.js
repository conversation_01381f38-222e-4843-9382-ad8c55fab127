const fs = require('fs');
const path = require('path');

class FileUtils {
  /**
   * Read a file and return its contents as a buffer
   * @param {string} filePath - Path to the file
   * @returns {Buffer|null} File contents as buffer or null if error
   */
  static readFileAsBuffer(filePath) {
    try {
      // Check if the path is a file before trying to read it
      if (!this.fileExists(filePath)) {
        console.warn(`Path is not a file or does not exist: ${filePath}`);
        return null;
      }
      return fs.readFileSync(filePath);
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Write buffer data to a file
   * @param {string} filePath - Path to the file
   * @param {Buffer} data - Data to write
   * @returns {boolean} True if successful, false otherwise
   */
  static writeBufferToFile(filePath, data) {
    try {
      fs.writeFileSync(filePath, data);
      return true;
    } catch (error) {
      console.error(`Error writing file ${filePath}:`, error);
      return false;
    }
  }

  /**
   * Check if a directory exists
   * @param {string} dirPath - Path to the directory
   * @returns {boolean} True if directory exists
   */
  static directoryExists(dirPath) {
    try {
      return fs.existsSync(dirPath) && fs.lstatSync(dirPath).isDirectory();
    } catch (error) {
      return false;
    }
  }

  /**
   * Create a directory if it doesn't exist
   * @param {string} dirPath - Path to the directory
   * @returns {boolean} True if successful or already exists, false otherwise
   */
  static createDirectory(dirPath) {
    try {
      if (!this.directoryExists(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
      return true;
    } catch (error) {
      console.error(`Error creating directory ${dirPath}:`, error);
      return false;
    }
  }

  /**
   * Get file name from path
   * @param {string} filePath - Path to the file
   * @returns {string} File name
   */
  static getFileName(filePath) {
    return path.basename(filePath);
  }

  /**
   * Get file extension
   * @param {string} filePath - Path to the file
   * @returns {string} File extension
   */
  static getFileExtension(filePath) {
    return path.extname(filePath);
  }

  /**
   * Check if a file exists and is actually a file (not a directory)
   * @param {string} filePath - Path to the file
   * @returns {boolean} True if file exists and is a file
   */
  static fileExists(filePath) {
    try {
      const stats = fs.statSync(filePath);
      return stats.isFile();
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if a path is a directory
   * @param {string} dirPath - Path to check
   * @returns {boolean} True if path is a directory
   */
  static isDirectory(dirPath) {
    try {
      const stats = fs.statSync(dirPath);
      return stats.isDirectory();
    } catch (error) {
      return false;
    }
  }

  /**
   * Get all files in a directory (non-recursive)
   * @param {string} dirPath - Path to the directory
   * @returns {Array<string>} Array of file paths
   */
  static getFilesInDirectory(dirPath) {
    try {
      if (!this.directoryExists(dirPath)) {
        console.warn(`Directory does not exist: ${dirPath}`);
        return [];
      }
      
      const entries = fs.readdirSync(dirPath);
      return entries.map(entry => path.join(dirPath, entry));
    } catch (error) {
      console.error(`Error reading directory ${dirPath}:`, error);
      return [];
    }
  }
}

module.exports = FileUtils;