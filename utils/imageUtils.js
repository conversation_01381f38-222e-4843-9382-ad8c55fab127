const fs = require('fs');
const path = require('path');

class ImageUtils {
  // Cache for thumbnail data URLs to improve performance
  static thumbnailCache = new Map();
  
  /**
   * Convert image data to base64 string
   * @param {Buffer} imageData - Image data buffer
   * @returns {string} Base64 encoded string
   */
  static imageToBase64(imageData) {
    return imageData.toString('base64');
  }

  /**
   * Convert base64 string to image buffer
   * @param {string} base64String - Base64 encoded string
   * @returns {Buffer} Image data buffer
   */
  static base64ToImage(base64String) {
    return Buffer.from(base64String, 'base64');
  }

  /**
   * Create a thumbnail from image data
   * @param {Buffer} imageData - Original image data
   * @param {number} width - Thumbnail width
   * @param {number} height - Thumbnail height
   * @returns {Promise<string>} Data URL of the thumbnail
   */
  static createThumbnail(imageData, width, height) {
    return new Promise((resolve, reject) => {
      // Create a cache key based on image data and dimensions
      const cacheKey = `${imageData.length}-${width}x${height}`;
      
      // Check if thumbnail is already cached
      if (this.thumbnailCache.has(cacheKey)) {
        resolve(this.thumbnailCache.get(cacheKey));
        return;
      }
      
      // Create a temporary image to work with
      const img = new Image();
      img.onload = function() {
        // Create canvas for thumbnail
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        
        // Enable nearest neighbor scaling for pixel-perfect rendering
        ctx.imageSmoothingEnabled = false;
        
        // Draw image with nearest neighbor scaling
        ctx.drawImage(img, 0, 0, width, height);
        
        // Convert to data URL
        const dataUrl = canvas.toDataURL('image/png');
        
        // Cache the thumbnail
        this.thumbnailCache.set(cacheKey, dataUrl);
        
        // Return data URL
        resolve(dataUrl);
      }.bind(this);
      img.onerror = reject;
      
      // Convert buffer to data URL
      const base64String = imageData.toString('base64');
      img.src = `data:image/png;base64,${base64String}`;
    });
  }

  /**
   * Check if a file is a PNG image
   * @param {string} filePath - Path to the file
   * @returns {boolean} True if file is a PNG image
   */
  static isPngFile(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return ext === '.png';
  }

  /**
   * Get all PNG files from a directory
   * @param {string} directoryPath - Path to the directory
   * @returns {Array<string>} Array of PNG file paths
   */
  static getPngFilesFromDirectory(directoryPath) {
    try {
      const files = fs.readdirSync(directoryPath);
      return files
        .filter(file => this.isPngFile(file))
        .map(file => path.join(directoryPath, file));
    } catch (error) {
      console.error('Error reading directory:', error);
      return [];
    }
  }

  /**
   * Validate if an image is 48x48 pixels
   * @param {Buffer} imageData - Image data buffer
   * @returns {boolean} True if image is 48x48 pixels
   */
  static is48x48Image(imageData) {
    // In a real implementation, we would parse the PNG header
    // to get the actual dimensions. This is a placeholder.
    // For now, we'll assume all images are valid.
    return true;
  }

  /**
   * Scale image data using nearest neighbor algorithm
   * @param {ImageData} imageData - Original image data
   * @param {number} scale - Scale factor
   * @returns {ImageData} Scaled image data
   */
  static scaleImageDataNearestNeighbor(imageData, scale) {
    const { width, height, data } = imageData;
    const newWidth = width * scale;
    const newHeight = height * scale;
    
    // Create new image data
    const newData = new Uint8ClampedArray(newWidth * newHeight * 4);
    
    for (let y = 0; y < newHeight; y++) {
      for (let x = 0; x < newWidth; x++) {
        // Find the corresponding pixel in the original image
        const srcX = Math.floor(x / scale);
        const srcY = Math.floor(y / scale);
        
        // Calculate indices
        const srcIdx = (srcY * width + srcX) * 4;
        const destIdx = (y * newWidth + x) * 4;
        
        // Copy pixel data
        newData[destIdx] = data[srcIdx];     // R
        newData[destIdx + 1] = data[srcIdx + 1]; // G
        newData[destIdx + 2] = data[srcIdx + 2]; // B
        newData[destIdx + 3] = data[srcIdx + 3]; // A
      }
    }
    
    return new ImageData(newData, newWidth, newHeight);
  }
  
  /**
   * Create thumbnail element for sidebar
   * @param {Buffer} imageData - Image data buffer
   * @param {string} filename - Name of the file
   * @returns {Promise<HTMLDivElement>} Thumbnail element
   */
  static async createThumbnailElement(imageData, filename) {
    // Create thumbnail container
    const thumbnail = document.createElement('div');
    thumbnail.className = 'thumbnail';
    
    // Create placeholder for thumbnail image
    const placeholder = document.createElement('div');
    placeholder.className = 'thumbnail-placeholder';
    thumbnail.appendChild(placeholder);
    
    // Generate thumbnail (148x148)
    try {
      const thumbnailDataUrl = await this.createThumbnail(imageData, 148, 148);
      
      // Replace placeholder with actual thumbnail
      const img = document.createElement('img');
      img.src = thumbnailDataUrl;
      img.style.width = '100%';
      img.style.height = '100%';
      img.style.borderRadius = '4px';
      img.style.objectFit = 'cover';
      
      // Clear placeholder and add image
      placeholder.replaceWith(img);
    } catch (error) {
      console.error('Error creating thumbnail for', filename, error);
    }
    
    return thumbnail;
  }
  
  /**
   * Clear thumbnail cache
   */
  static clearThumbnailCache() {
    this.thumbnailCache.clear();
  }
}

module.exports = ImageUtils;