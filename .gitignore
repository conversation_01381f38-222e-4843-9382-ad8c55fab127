# ===== macOS =====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
.AppleDouble
.LSOverride
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# ===== Xcode =====
# Build generated
build/
DerivedData/

# Various settings
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/

# Other
*.moved-aside
*.xccheckout
*.xcscmblueprint

# Obj-C/Swift specific
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

# CocoaPods
Pods/
*.xcworkspace

# Carthage
Carthage/Build/
Carthage/Checkouts/

# Swift Package Manager
.build/
.swiftpm/
Package.resolved

# Xcode Patch
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
!*.xcodeproj/xcshareddata/
!*.xcodeproj/project.xcworkspace/
*.xcodeproj/project.xcworkspace/xcshareddata/

# Archives
*.xcarchive

# ===== VS Code =====
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Local History for Visual Studio Code
.history/

# ===== Nova =====
.nova/

# ===== General Development =====
# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~
.cache/

# Node.js
node_modules/
jspm_packages/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/

# Java
*.class
target/
*.jar
*.war
*.ear

# .NET
bin/
obj/
*.dll
*.exe

# Rust
target/
Cargo.lock

# Go
*.exe~
*.test
*.out

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Database
*.db
*.sqlite
*.sqlite3

# IDE and Editor files
.idea/
*.sublime-project
*.sublime-workspace
*~
.project
.metadata
.classpath
.c9/
*.launch
.settings/
.loadpath
.recommenders
.externalToolBuilders/
.cproject
.autotools
.factorypath
.buildpath
.target
.texlipse
.springBeans
.recommenders/
.apt_generated/
.cache-main
.scala_dependencies
.worksheet
