Pixel Editor - Electron App Development Prompt
Project Overview
Create a simple pixel image editor built with HTML, CSS, JavaScript, and Electron for macOS. The editor focuses on editing collections of small 48x48 pixel PNG images with real-time saving using SQLite for project storage.

Technical Requirements
Platform: Electron app for macOS
Languages: HTML, CSS, JavaScript, Node.js
Database: SQLite for project storage (use better-sqlite3 npm package)
Image Format: PNG only (48x48 pixels, 72 DPI input files)
Canvas Size: 512x512 pixels for editing
Preview Size: 48x48 pixels
Core Features & UI Components
Main Interface Layout
Pick Folder Button: Opens folder selection dialog to load all PNG files
Save/Load Project Buttons: Manages .sqlite project files
Scrollable Sidebar: Shows loaded images scaled to 148x148px (nearest neighbor)
Red dot indicator: Image has been edited
Green dot indicator: Currently active image being edited
Main Canvas: 512x512 pixel drawing area
Preview Window: 48x48 pixel size preview of current image
Color Picker: For selecting active drawing color (default: black)
Toggle Grid Button: Show/hide pixel grid lines (subtle gray)
Tools
Pencil Tool:
Click to draw single pixels with active color
Click and drag for continuous drawing
Option+Click to sample color from canvas
Fill Tool: Bucket fill clicked regions with active color
Toggle View Tool: Overlay original image on canvas for comparison
Project Workflow
User selects folder containing 48x48 PNG files (approximately 35 images)
Prompt for project name immediately
Create .sqlite project file with embedded original image data
Load all images into sidebar with edit indicators
Real-time editing with batched saves (every 2-3 seconds or after 10 edits)
Each edit updates both the actual PNG file and SQLite database
Database Schema (SQLite)
sql
CREATE TABLE projects (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    folder_path TEXT
);

CREATE TABLE images (
    id INTEGER PRIMARY KEY,
    project_id INTEGER,
    filename TEXT NOT NULL,
    original_data BLOB,
    current_data BLOB,
    edited BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (project_id) REFERENCES projects (id)
);
Implementation Steps
Phase 1: Electron Setup & Basic UI
Initialize Electron project with proper directory structure
Set up main process and renderer process
Create basic HTML layout with canvas and sidebar
Implement folder picker using Electron's dialog API
Add SQLite integration with better-sqlite3
Phase 2: Image Loading & Storage
Load PNG files from selected folder
Create SQLite database and store original image data as BLOB
Display images in scrollable sidebar (148x148 scaled thumbnails)
Implement image selection (green dot indicator)
Phase 3: Drawing Tools & Canvas
Set up 512x512 HTML5 canvas with proper scaling
Implement pencil tool with pixel-perfect drawing
Add color picker functionality
Create fill tool using flood-fill algorithm
Add option-click color sampling
Phase 4: Real-time Saving System
Implement debounced saving (batch saves every 2-3 seconds)
Update both PNG files and SQLite on edits
Add edit indicators (red dots) to sidebar thumbnails
Create 48x48 preview window that updates in real-time
Phase 5: Additional Features
Toggle view tool (original overlay)
Revert functionality (restore from original data)
Pixel grid toggle with subtle gray lines
Project save/load functionality
Single undo capability (optional, low priority)
Phase 6: Polish & Optimization
Error handling for corrupted files or invalid folders
Loading states and progress indicators
Memory optimization for multiple images
macOS-specific UI polish and menu integration
Key Technical Considerations
Canvas Scaling: 384x384 display represents exactly 48x48 pixels - each logical pixel is exactly 8x8 screen pixels
Pixel-Perfect Coordinate Mapping: pixelX = Math.floor(mouseX / 8), pixelY = Math.floor(mouseY / 8)
Performance-First Design: Use ImageData manipulation, stack-based algorithms, and batched operations
Real-time Preview: 48x48 preview window must update immediately with every canvas change
Use nearest-neighbor scaling to maintain pixel art sharpness
Implement efficient canvas-to-PNG conversion for real-time saves
Handle transparency by treating as white background
Ensure atomic database writes to prevent corruption
Optimize for 35+ small images loaded simultaneously
Color Sampling: Option+click works for both pencil and fill tools
Success Criteria
Single executable Electron app that runs on double-click
Smooth pixel-level editing experience
Reliable real-time saving with no data loss
Intuitive interface for managing multiple small pixel art images
.sqlite project files that can be shared and reopened
File Structure Expected
pixel-editor/
├── main.js (Electron main process)
├── renderer.js (Canvas and UI logic)
├── index.html (Main interface)
├── style.css (UI styling)
├── package.json (Dependencies)
└── database.js (SQLite operations)
Build this as a focused, professional tool for pixel art editing with emphasis on reliability and ease of use.

