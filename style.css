/* CSS VARIABLES FOR EASY THEME CUSTOMIZATION */
:root {
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    --bg-color: #1e1e1e;
    --panel-color: #2d2d2d;
    --panel-darker-color: #252526;
    --border-color: #444;
    --text-color: #cccccc;
    --text-color-dark: #888;
    --accent-color: #0e639c;
    --active-color: #33a34a;
    --edited-color: #d94f4f;
    --panel-border-radius: 8px;
}

/* BASIC RESET & BODY STYLES */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-color);
    color: var(--text-color);
    margin: 1rem;
}

.app-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: calc(100vh - 2rem);
    width: 100%;
}

/* HEADER STYLES */
.app-header {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    background-color: var(--panel-darker-color);
    border-radius: var(--panel-border-radius);
}

.header-btn {
    background-color: #3c3c3c;
    color: var(--text-color);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}
.header-btn:hover {
    background-color: #4f4f4f;
}

/* MAIN LAYOUT (SIDEBAR + WORKSPACE) */
.main-content {
    display: grid;
    grid-template-columns: 180px 1fr; /* Sidebar width + remaining space */
    gap: 1rem;
    flex-grow: 1;
    min-height: 0; /* Important for flex/grid children with scrolling */
}

/* IMAGE SIDEBAR */
.image-sidebar {
    background-color: var(--panel-darker-color);
    border-radius: var(--panel-border-radius);
    padding: 0.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Thumbnail container that holds both the thumbnail and the status indicator */
.thumbnail-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.75rem;
    position: relative;
    padding: 0 0.25rem;
}

.thumbnail {
    width: 148px;
    height: 148px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.2s;
    position: relative;
    flex-shrink: 0; /* Prevent thumbnails from shrinking */
}
.thumbnail:hover {
    border-color: #555;
}
.thumbnail.active {
    border-color: var(--active-color);
}
.thumbnail-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #3a3a3a, #4a4a4a);
    border-radius: 4px;
}

/* Status indicator dot positioned next to the thumbnail */
.status-indicator {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid var(--panel-darker-color);
    background-color: transparent;
    flex-shrink: 0;
    transition: all 0.2s;
    opacity: 0.3;
}

.status-indicator.edited {
    background-color: var(--edited-color);
    opacity: 1;
    box-shadow: 0 0 4px rgba(255, 69, 58, 0.5);
}

.status-indicator.active {
    border-color: var(--active-color);
    opacity: 1;
}

/* EDITOR WORKSPACE */
.editor-workspace {
    background-color: var(--panel-color);
    border-radius: var(--panel-border-radius);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-name-display {
    background-color: var(--panel-darker-color);
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-size: 0.9rem;
}

.preview-box {
    width: 48px;
    height: 48px;
    background-color: var(--panel-darker-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

/* STAGE (TOOLBAR + CANVAS) */
.stage {
    flex-grow: 1;
    display: grid;
    grid-template-columns: 60px 1fr 60px; /* Toolbar | Canvas | Actions */
    gap: 1rem;
    min-height: 0;
}

/* TOOLBAR */
.toolbar {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.tool-btn {
    width: 44px;
    height: 44px;
    border-radius: 6px;
    background-color: var(--panel-darker-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    display: grid;
    place-items: center;
    transition: all 0.2s;
}
.tool-btn:hover {
    background-color: #444;
}
.tool-btn.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

#color-picker {
    width: 44px;
    height: 44px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: none;
    cursor: pointer;
    padding: 0;
}



/* CANVAS AREA */
.canvas-container {
    background-color: var(--panel-darker-color);
    border-radius: var(--panel-border-radius);
    display: grid;
    place-items: center;
    padding: 1rem;
    overflow: auto;
    min-height: 0;
}

.canvas-placeholder {
    /* Dimensions will be set dynamically by JavaScript */
    background-color: #fff;
    /* Checkerboard pattern for transparency */
    background-image:
        linear-gradient(45deg, #ccc 25%, transparent 25%),
        linear-gradient(-45deg, #ccc 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #ccc 75%),
        linear-gradient(-45deg, transparent 75%, #ccc 75%);
    background-size: 16px 16px;
    background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
    box-shadow: 0 0 10px rgba(0,0,0,0.5);
}

#editor-canvas {
    /* Dimensions will be set dynamically by JavaScript */
    /* CRITICAL: Force nearest neighbor scaling - no smoothing! */
    image-rendering: -moz-crisp-edges;
    image-rendering: -webkit-crisp-edges;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -o-crisp-edges;
    image-rendering: pixelated;
    image-rendering: crisp-edges;
    cursor: crosshair;
    /* Additional anti-smoothing properties */
    -ms-interpolation-mode: nearest-neighbor;
}

/* CANVAS ACTIONS (UNDO/REDO) */
.canvas-actions {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.action-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    color: var(--text-color-dark);
    cursor: pointer;
    display: grid;
    place-items: center;
    transition: all 0.2s;
}
.action-btn:hover {
    background-color: #3c3c3c;
    color: var(--text-color);
}
