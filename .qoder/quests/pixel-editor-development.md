# Pixel Editor - Electron App Design Document

## 1. Overview

The Pixel Editor is an Electron-based desktop application for macOS that allows users to edit collections of small 48x48 pixel PNG images. The application provides a specialized environment for pixel art editing with real-time saving capabilities using SQLite for project storage.

### Key Features
- Edit multiple 48x48 pixel PNG images simultaneously
- Real-time saving with SQLite database storage
- Specialized pixel-perfect drawing tools
- Project management with save/load functionality
- Preview window showing actual image size

## 2. Architecture

### 2.1 System Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Electron Application                     │
├─────────────────────────────────────────────────────────────┤
│  Main Process        │     Renderer Process                 │
│                      │                                      │
│  ┌─────────────┐    │    ┌─────────────────────────────┐   │
│  │ Electron    │    │    │   User Interface            │   │
│  │ Main        │    │    │                             │   │
│  │ Process     │◄───┼───►│  ┌──────────────────────┐   │   │
│  │             │    │    │  │   Header Section     │   │   │
│  │ • Window    │    │    │  │  • Action Buttons    │   │   │
│  │   Management│    │    │  └──────────────────────┘   │   │
│  │ • File      │    │    │  ┌──────────────────────┐   │   │
│  │   Operations│    │    │  │   Main Content       │   │   │
│  │ • Database  │    │    │  │                      │   │   │
│  │   Access    │    │    │  │ • Image Sidebar      │   │   │
│  │ • Menu      │    │    │  │ • Editor Workspace   │   │   │
│  │   System    │    │    │  │   - Preview Header   │   │   │
│  │             │    │    │  │   - Stage Area       │   │   │
│  └─────────────┘    │    │  │     * Toolbar        │   │   │
│                      │    │  │     * Canvas         │   │   │
│                      │    │  │     * Actions        │   │   │
│                      │    │  └──────────────────────┘   │   │
└──────────────────────┼────┼──────────────────────────────┘   │
                       │    │                                  │
                       │    │                                  │
┌──────────────────────▼────▼──────────────────────────────────┐
│                    SQLite Database                           │
│                                                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Projects Table                                          │ │
│  │                                                         │ │
│  │ • Project identification and metadata                  │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Images Table                                            │ │
│  │                                                         │ │
│  │ • Image data and edit status                           │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Technology Stack
- **Platform**: Electron for macOS desktop application
- **Frontend**: HTML5, CSS3, JavaScript
- **Backend**: Node.js
- **Database**: SQLite with better-sqlite3 package
- **Graphics**: HTML5 Canvas API
- **UI Framework**: Vanilla JavaScript with Phosphor Icons
- **Build Tools**: npm, Electron Builder

### 2.3 File Structure
```
pixel-editor/
├── main.js                 # Electron main process
├── index.html              # Main application interface
├── style.css               # Application styling
├── renderer.js             # Client-side JavaScript logic
├── database.js             # SQLite database operations
├── utils/                  # Helper functions and utilities
│   ├── imageUtils.js       # Image processing functions
│   └── fileUtils.js        # File operation helpers
└── assets/                 # Icons and other resources
```

## 3. Component Architecture

### 3.1 Main Process
Responsible for:
- Creating and managing the application window
- Handling file system operations
- Managing the SQLite database connection
- Implementing the application menu
- Handling inter-process communication with the renderer

### 3.2 Renderer Process
Comprised of several UI components:

#### 3.2.1 Header Component
- **Functionality**:
  - Load Folder button for selecting image directories
  - Open Project button for loading existing projects
  - Save Project button for saving current work

#### 3.2.2 Image Sidebar Component
- **Display**: Scrollable list of loaded images
- **Thumbnail Size**: 148x148 pixels with nearest-neighbor scaling
- **Indicators**:
  - Red dot: Image has been edited
  - Green dot: Currently active image
- **Functionality**:
  - Image selection
  - Visual edit status indicators

#### 3.2.3 Editor Workspace Component
- **Preview Header**:
  - File name display
  - 48x48 pixel preview window showing actual image size
- **Stage Area**:
  - Toolbar with drawing tools
  - 512x512 pixel canvas for editing
  - Actions panel with revert functionality

#### 3.2.4 Tools
- **Pencil Tool**: Draw single pixels or continuous lines
- **Fill Tool**: Flood fill connected regions
- **Toggle View Tool**: Overlay original image for comparison
- **Color Picker**: Select active drawing color

### 3.3 Database Layer
Handles all SQLite operations:
- Project creation and management
- Image data storage (original and current versions)
- Edit status tracking
- Batch saving operations

## 4. Data Flow

### 4.1 Application Startup
1. User launches the application
2. Main process creates the application window
3. Renderer process initializes UI components
4. User selects a folder containing PNG files

### 4.2 Project Initialization
1. User selects folder with 48x48 PNG files
2. Application prompts for project name
3. SQLite database is created with project information
4. Original image data is stored as BLOBs in database
5. Thumbnails are generated and displayed in sidebar

### 4.3 Editing Workflow
1. User selects an image from the sidebar
2. Image data is loaded to the main canvas
3. User edits using drawing tools
4. Changes are immediately reflected in:
   - Main canvas (512x512)
   - Preview window (48x48)
5. Edit status is tracked in database
6. Changes are batch-saved to:
   - SQLite database
   - Original PNG files

### 4.4 Saving Mechanism
1. Debounced saving every 2-3 seconds
2. After 10 edits (whichever comes first)
3. Atomic database writes to prevent corruption
4. Simultaneous updates to both database and PNG files

## 5. UI/UX Design

### 5.1 Main Interface Layout

The application interface is structured as follows:

1. **Header Section** - Contains primary action buttons:
   - Load Folder button
   - Open Project button
   - Save Project button

2. **Main Content Area** - Divided into two sections:
   - **Image Sidebar** (180px wide):
     - Scrollable list of image thumbnails (148x148px)
     - Visual indicators for edited (red dot) and active (green dot) images
   - **Editor Workspace** (remaining space):
     - Preview Header with file name display and 48x48px preview window
     - Stage area with:
       - Left Toolbar (60px wide) with drawing tools and color picker
       - Central Canvas Container (512x512px) for image editing
       - Right Actions Panel (60px wide) with revert functionality

The layout uses a grid-based design with a dark theme optimized for pixel art editing.

### 5.2 Color Scheme
- **Primary Background**: #1e1e1e (dark gray theme for reduced eye strain)
- **Panel Background**: #2d2d2d (slightly lighter gray for UI panels)
- **Darker Panel Background**: #252526 (for contrast in nested elements)
- **Accent Color**: #0e639c (blue for active elements)
- **Active Indicator**: #33a34a (green for currently selected items)
- **Edit Indicator**: #d94f4f (red for modified images)
- **Text Color**: #cccccc (light gray for readability)
- **Border Color**: #444 (subtle borders for UI elements)

## 6. Database Schema

### 6.1 Projects Table
Structure for storing project information including identifiers, names, creation timestamps, and folder paths.

### 6.2 Images Table
Structure for storing image data including references to projects, filenames, original and current image data, and edit status.

## 7. Core Algorithms

### 7.1 Pixel Coordinate Mapping
A mapping system to ensure pixel-perfect drawing by translating between screen coordinates and logical pixel positions.

### 7.2 Flood Fill Algorithm
A stack-based algorithm for filling connected regions with a selected color.

### 7.3 Color Sampling
A mechanism for extracting colors from the canvas to use as the active drawing color.

## 8. Performance Considerations

### 8.1 Memory Optimization
Strategies for efficient memory usage when handling multiple images simultaneously.

### 8.2 Real-time Updates
Techniques for providing responsive user interface updates while managing file I/O operations.

### 8.3 Image Processing
Optimization approaches for maintaining image quality and processing efficiency.

## 9. Error Handling

### 9.1 File Operations
Approaches for validating inputs and handling file-related errors gracefully.

### 9.2 Database Operations
Techniques for ensuring data integrity and handling database-related issues.

### 9.3 UI States
Methods for providing clear feedback to users during various application states.

## 10. Testing Strategy

### 10.1 Unit Testing
Testing approach for individual components and functions.

### 10.2 Integration Testing
Testing approach for component interactions and workflows.

### 10.3 User Acceptance Testing
Testing approach for validating user experience and performance.